import React from 'react';
import { Controller } from 'react-hook-form';
import PropTypes from 'prop-types';

/**
 * PostAsSelector Component
 * Standalone component for post as radio button selection
 */
const PostAsSelector = ({ control, errors = {}, error, onChange, value, name = 'postAs' }) => {
  // Handle both error (string) and errors (object) props
  const errorMessage = error || errors?.postAs;
  const options = [
    { value: 'Creator', label: 'Creator' },
    { value: 'Group', label: 'Group' },
    { value: 'Member', label: 'Member' }
  ];

  // If control is not provided, render as uncontrolled component
  if (!control) {
    return (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Post as <span className="text-[#3D9D9B]">*</span>
        </label>
        <div className="flex items-center space-x-6">
          {options.map((option) => (
            <label key={option.value} className="flex items-center">
              <input
                type="radio"
                name={name}
                value={option.value}
                checked={value === option.value}
                onChange={(e) => {
                  if (onChange) onChange(e.target.value);
                }}
                className="w-4 h-4 text-[#3D9D9B] border-gray-300 focus:ring-[#3D9D9B] accent-[#3D9D9B]"
              />
              <span className="ml-2 text-sm text-gray-700">{option.label}</span>
            </label>
          ))}
        </div>
        {errorMessage && (
          <p className="mt-1 text-sm text-red-600">{errorMessage}</p>
        )}
      </div>
    );
  }

  // If control is provided, render as controlled component with react-hook-form
  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Post as <span className="text-[#3D9D9B]">*</span>
      </label>
      <Controller
        name={name}
        control={control}
        render={({ field: { onChange: fieldOnChange, value } }) => (
          <div className="flex items-center space-x-6">
            {options.map((option) => (
              <label key={option.value} className="flex items-center">
                <input
                  type="radio"
                  value={option.value}
                  checked={value === option.value}
                  onChange={(e) => {
                    fieldOnChange(e.target.value);
                    if (onChange) onChange(e.target.value);
                  }}
                  className="w-4 h-4 text-[#3D9D9B] border-gray-300 focus:ring-[#3D9D9B] accent-[#3D9D9B]"
                />
                <span className="ml-2 text-sm text-gray-700">{option.label}</span>
              </label>
            ))}
          </div>
        )}
      />
      {errorMessage && (
        <p className="mt-1 text-sm text-red-600">{errorMessage}</p>
      )}
    </div>
  );
};

PostAsSelector.propTypes = {
  control: PropTypes.object,
  errors: PropTypes.object,
  error: PropTypes.string,
  onChange: PropTypes.func,
  value: PropTypes.string,
  name: PropTypes.string
};

export default PostAsSelector;
