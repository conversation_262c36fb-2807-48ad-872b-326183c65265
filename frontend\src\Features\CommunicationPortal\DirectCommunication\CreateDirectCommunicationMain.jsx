import React, { useState, useEffect } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { ArrowLeft, Upload, X } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import DirectCommunicationPreview from './components/DirectCommunicationPreview';
import PriorityDropdown from './components/PriorityDropdown';
import LabelSelector from './components/LabelSelector';
import Calendar from './components/Calendar';
import TimePicker from './components/TimePicker';
import TowerSelector from './components/TowerSelector';
import UnitSelector from './components/UnitSelector';
import MemberSelector from './components/MemberSelector';
import GroupSelector from './components/GroupSelector';
import MessageBox from '../../../Components/MessageBox/MessageBox';
import ErrorMessage from '../../../Components/MessageBox/ErrorMessage';

// Validation schema
const directCommunicationSchema = yup.object().shape({
  title: yup
    .string()
    .required('Title is required')
    .test('word-count', 'Title must be 10 words or less', (value) => {
      if (!value) return true;
      return value.trim().split(/\s+/).length <= 10;
    }),
  description: yup
    .string()
    .required('Description is required')
    .test('word-count', 'Description must be 100 words or less', (value) => {
      if (!value) return true;
      return value.trim().split(/\s+/).length <= 100;
    }),
  postAs: yup.string().required('Post as selection is required'),
  creatorName: yup.string().required('Creator name is required'),
  selectedMemberId: yup.string().when('postAs', {
    is: 'Member',
    then: (schema) => schema.required('Please select a member'),
    otherwise: (schema) => schema.notRequired()
  }),
  selectedMemberName: yup.string().when('postAs', {
    is: 'Member',
    then: (schema) => schema.required('Please select a member'),
    otherwise: (schema) => schema.notRequired()
  }),
  selectedGroupId: yup.string().when('postAs', {
    is: 'Group',
    then: (schema) => schema.required('Please select a group'),
    otherwise: (schema) => schema.notRequired()
  }),
  selectedGroupName: yup.string().when('postAs', {
    is: 'Group',
    then: (schema) => schema.required('Please select a group'),
    otherwise: (schema) => schema.notRequired()
  }),
  priority: yup
    .string()
    .required('Priority is required')
    .oneOf(['low', 'normal', 'high', 'urgent'], 'Invalid priority value'),
  label: yup.string().required('Label is required'),
  startDate: yup.string().required('Start date is required'),
  startTime: yup.string().required('Start time is required'),
  endDate: yup.string().required('End date is required'),
  endTime: yup.string().required('End time is required'),
  selectedTowers: yup.array(),
  selectedUnits: yup.array(),
  attachments: yup.array()
});

const CreateDirectCommunication = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [currentUser, setCurrentUser] = useState(null);
  const [attachments, setAttachments] = useState([]);
  const [successMessage, setSuccessMessage] = useState('');
  const [fileUploadError, setFileUploadError] = useState('');
  const [apiError, setApiError] = useState('');
  const [towerError, setTowerError] = useState('');
  const [unitError, setUnitError] = useState('');
  const [formError, setFormError] = useState('');
  const [titleError, setTitleError] = useState('');
  const [descriptionError, setDescriptionError] = useState('');
  const [priorityError, setPriorityError] = useState('');
  const [labelError, setLabelError] = useState('');
  const [startDateError, setStartDateError] = useState('');
  const [startTimeError, setStartTimeError] = useState('');
  const [endDateError, setEndDateError] = useState('');
  const [endTimeError, setEndTimeError] = useState('');
  const [creatorNameError, setCreatorNameError] = useState('');
  const [postAsError, setPostAsError] = useState('');

  // Get the source tab from location state
  const sourceTab = location.state?.sourceTab || null;

  // Get saved postAs preference from localStorage
  const getSavedPostAsPreference = () => {
    try {
      return localStorage.getItem('directCommunicationPostAs') || '';
    } catch (error) {
      console.error('Error getting saved postAs preference:', error);
      return '';
    }
  };

  // Save postAs preference to localStorage
  const savePostAsPreference = (value) => {
    localStorage.setItem('directCommunicationPostAs', value);
  };

  // Form setup with validation
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    getValues,
    formState: { errors, isSubmitting, isValid }
  } = useForm({
    resolver: yupResolver(directCommunicationSchema),
    mode: 'onChange',
    defaultValues: {
      title: '',
      description: '',
      postAs: getSavedPostAsPreference(),
      creatorName: '',
      selectedMemberId: '',
      selectedMemberName: '',
      selectedGroupId: '',
      selectedGroupName: '',
      priority: 'normal',
      label: '',
      startDate: '',
      startTime: '',
      endDate: '',
      endTime: '',
      selectedTowers: [],
      selectedUnits: [],
      attachments: []
    }
  });

  // Handle title input change to limit words
  const handleTitleChange = (value, onChange) => {
    if (!value || value.trim() === '') {
      onChange('');
      return;
    }

    const words = value.trim().split(/\s+/);
    const limited = words.slice(0, 10).join(' ');
    onChange(limited);
  };

  // Check if all required fields are filled
  const isFormValid = () => {
    const values = getValues();
    return (
      values.title &&
      values.description &&
      values.creatorName &&
      values.priority &&
      values.label &&
      values.startDate &&
      values.startTime &&
      values.endDate &&
      values.endTime &&
      values.selectedTowers?.length > 0 &&
      values.selectedUnits?.length > 0 &&
      (values.postAs === 'Creator' ||
        (values.postAs === 'Group' && values.selectedGroupId) ||
        (values.postAs === 'Member' && values.selectedMemberId))
    );
  };

  // Watch all form values for real-time preview
  const watchedValues = watch();

  // Watch creator name for auto-sync
  const creatorName = watch('creatorName');

  // Watch selected towers for unit filtering
  const selectedTowers = watch('selectedTowers');

  // Clear tower/unit errors when selections change
  useEffect(() => {
    if (selectedTowers && selectedTowers.length > 0) {
      setTowerError('');
    }
  }, [selectedTowers]);

  const selectedUnits = watch('selectedUnits');

  // Handle form submission
  const onSubmit = async (data) => {
    try {
      // Clear all error states
      clearAllErrors();

      // Validate form
      const hasErrors = Object.keys(errors).length > 0;
      if (hasErrors) {
        setFormError('Please fill in all required fields to continue.');
        return;
      }

      console.log('Direct Communication Data:', data);
      console.log('Attachments:', attachments);

      // Import the backend API
      const { directCommunicationApi, formatDirectCommunicationForApi } = await import('../../../api/directCommunicationApi/directCommunicationBackendApi');

      // Format data for API
      const apiData = formatDirectCommunicationForApi(data, attachments);

      console.log('Formatted API Data:', apiData);

      // Create direct communication via API
      const createdCommunication = await directCommunicationApi.createDirectCommunication(apiData);

      console.log('Direct Communication created successfully:', createdCommunication);

      // Show success message
      setSuccessMessage('Direct Communication has been successfully Created.');

    } catch (error) {
      console.error('Error creating direct communication:', error);
      setApiError(error.message || 'Failed to create direct communication');
    }
  };

  // Handle errors
  const onError = (errors) => {
    console.log('Form validation errors:', errors);

    // Clear all errors first
    clearAllErrors();

    // Set specific error messages for each field
    if (errors.creatorName) {
      setCreatorNameError('Creator name is required.');
    }

    if (errors.postAs) {
      setPostAsError('Please select how you want to post this communication.');
    }

    if (errors.title) {
      setTitleError('Title is required.');
    }

    if (errors.description) {
      setDescriptionError('Description is required.');
    }

    if (errors.priority) {
      setPriorityError('Please select a priority level.');
    }

    if (errors.label) {
      setLabelError('Please select a label.');
    }

    if (errors.startDate) {
      setStartDateError('Start date is required.');
    }

    if (errors.startTime) {
      setStartTimeError('Start time is required.');
    }

    if (errors.endDate) {
      setEndDateError('End date is required.');
    }

    if (errors.endTime) {
      setEndTimeError('End time is required.');
    }

    // Set a general form error message
    const errorFields = Object.keys(errors);
    if (errorFields.length > 0) {
      setFormError('Please fill in all required fields correctly before submitting.');
    }
  };

  // Clear all error states
  const clearAllErrors = () => {
    setFormError('');
    setTitleError('');
    setDescriptionError('');
    setPriorityError('');
    setLabelError('');
    setStartDateError('');
    setStartTimeError('');
    setEndDateError('');
    setEndTimeError('');
    setCreatorNameError('');
    setPostAsError('');
    setTowerError('');
    setUnitError('');
    setApiError('');
    setFileUploadError('');
  };

  // Handle back navigation
  const handleBack = () => {
    const targetTab = sourceTab || 1;
    navigate('/direct-communications', {
      state: { activeTab: targetTab },
      replace: true
    });
  };

  // Clear success message
  const clearMessage = () => {
    setSuccessMessage('');
  };

  // Handle success message OK button
  const handleSuccessOk = () => {
    clearMessage();
    handleBack();
  };

  // Prepare preview data
  const previewData = {
    ...watchedValues,
    attachments
  };

  return (
    <div className="min-h-screen bg-[#F5F5F5]">
      {/* Header */}
      <div className="shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <button
              onClick={handleBack}
              className="flex items-center text-gray-600 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              <span className="text-lg font-semibold">Create Direct Communication</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Left Column - Preview */}
          <div className="order-2 lg:order-1 lg:col-span-4">
            <div className="bg-white rounded-lg shadow-sm p-6 lg:sticky lg:top-8 h-screen lg:h-[calc(100vh-6rem)] overflow-y-auto">
              <DirectCommunicationPreview data={previewData} currentUser={currentUser} />
            </div>
          </div>

          {/* Right Column - Form */}
          <div className="order-1 lg:order-2 lg:col-span-8">
            <form onSubmit={handleSubmit(onSubmit, onError)} className="space-y-6">
              {/* Communication Author Section */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-[#3D9D9B] mb-4">Communication Author</h3>

                {/* Creator Name */}
                <div className="mb-4">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Creator Name <span className="text-[#3D9D9B]">*</span>
                  </label>
                  <Controller
                    name="creatorName"
                    control={control}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="text"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3D9D9B] focus:border-transparent"
                        placeholder="Enter creator name"
                        readOnly={false}
                      />
                    )}
                  />
                  {errors.creatorName && (
                    <ErrorMessage message={creatorNameError} />
                  )}
                </div>

                {/* Post As */}
                <div className="mb-4">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Post as <span className="text-[#3D9D9B]">*</span>
                  </label>
                  <Controller
                    name="postAs"
                    control={control}
                    render={({ field }) => (
                      <div className="flex items-center space-x-6">
                        <label className="flex items-center">
                          <input
                            type="radio"
                            value="Creator"
                            checked={field.value === 'Creator'}
                            onChange={(e) => {
                              savePostAsPreference(e.target.value);
                              field.onChange(e.target.value);
                            }}
                            className="w-4 h-4 text-[#3D9D9B] border-gray-300 focus:ring-[#3D9D9B]"
                          />
                          <span className="ml-2 text-sm text-gray-700">Creator</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            value="Group"
                            checked={field.value === 'Group'}
                            onChange={(e) => {
                              savePostAsPreference(e.target.value);
                              field.onChange(e.target.value);
                            }}
                            className="w-4 h-4 text-[#3D9D9B] border-gray-300 focus:ring-[#3D9D9B]"
                          />
                          <span className="ml-2 text-sm text-gray-700">Group</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            value="Member"
                            checked={field.value === 'Member'}
                            onChange={(e) => {
                              savePostAsPreference(e.target.value);
                              field.onChange(e.target.value);
                            }}
                            className="w-4 h-4 text-[#3D9D9B] border-gray-300 focus:ring-[#3D9D9B]"
                          />
                          <span className="ml-2 text-sm text-gray-700">Member</span>
                        </label>
                      </div>
                    )}
                  />
                  {errors.postAs && (
                    <ErrorMessage message={postAsError} />
                  )}
                </div>

                {/* Group/Member Selector based on postAs */}
                {watch('postAs') === 'Group' && (
                  <div className="mb-4">
                    <GroupSelector
                      value={watch('selectedGroupId')}
                      onChange={(groupId, groupName) => {
                        setValue('selectedGroupId', groupId);
                        setValue('selectedGroupName', groupName);
                      }}
                      error={errors.selectedGroupId?.message}
                    />
                  </div>
                )}

                {watch('postAs') === 'Member' && (
                  <div className="mb-4">
                    <MemberSelector
                      value={watch('selectedMemberId')}
                      onChange={(memberId, memberName) => {
                        setValue('selectedMemberId', memberId);
                        setValue('selectedMemberName', memberName);
                      }}
                      error={errors.selectedMemberId?.message}
                    />
                  </div>
                )}
              </div>

              {/* Communication Information Section */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-[#3D9D9B] mb-4">Communication Information</h3>

                {/* Title */}
                <div className="mb-4">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Title <span className="text-[#3D9D9B]">*</span>
                  </label>
                  <Controller
                    name="title"
                    control={control}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="text"
                        onChange={(e) => handleTitleChange(e.target.value, field.onChange)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3D9D9B] focus:border-transparent"
                        placeholder="Communication Title (max 10 words)"
                      />
                    )}
                  />
                  {errors.title && (
                    <ErrorMessage message={titleError} />
                  )}
                </div>

                {/* Description */}
                <div className="mb-4">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Description <span className="text-[#3D9D9B]">*</span>
                  </label>
                  <Controller
                    name="description"
                    control={control}
                    render={({ field }) => (
                      <textarea
                        {...field}
                        rows={4}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3D9D9B] focus:border-transparent"
                        placeholder="Enter communication description (max 100 words)"
                      />
                    )}
                  />
                  {errors.description && (
                    <ErrorMessage message={descriptionError} />
                  )}
                </div>

                {/* Priority and Label */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Priority <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="priority"
                      control={control}
                      render={({ field }) => (
                        <PriorityDropdown
                          value={field.value}
                          onChange={field.onChange}
                        />
                      )}
                    />
                    {errors.priority && (
                      <ErrorMessage message={priorityError} />
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Label <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="label"
                      control={control}
                      render={({ field }) => (
                        <LabelSelector
                          value={field.value}
                          onChange={field.onChange}
                        />
                      )}
                    />
                    {errors.label && (
                      <ErrorMessage message={labelError} />
                    )}
                  </div>
                </div>

                {/* Date and Time Selection */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Start Date <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="startDate"
                      control={control}
                      render={({ field }) => (
                        <Calendar
                          value={field.value}
                          onChange={field.onChange}
                        />
                      )}
                    />
                    {errors.startDate && (
                      <ErrorMessage message={startDateError} />
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Start Time <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="startTime"
                      control={control}
                      render={({ field }) => (
                        <TimePicker
                          value={field.value}
                          onChange={field.onChange}
                        />
                      )}
                    />
                    {errors.startTime && (
                      <ErrorMessage message={startTimeError} />
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      End Date <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="endDate"
                      control={control}
                      render={({ field }) => (
                        <Calendar
                          value={field.value}
                          onChange={field.onChange}
                        />
                      )}
                    />
                    {errors.endDate && (
                      <ErrorMessage message={endDateError} />
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      End Time <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="endTime"
                      control={control}
                      render={({ field }) => (
                        <TimePicker
                          value={field.value}
                          onChange={field.onChange}
                        />
                      )}
                    />
                    {errors.endTime && (
                      <ErrorMessage message={endTimeError} />
                    )}
                  </div>
                </div>

                {/* Tower and Unit Selection */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Select Towers <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="selectedTowers"
                      control={control}
                      render={({ field }) => (
                        <TowerSelector
                          value={field.value}
                          onChange={field.onChange}
                        />
                      )}
                    />
                    {errors.selectedTowers && (
                      <ErrorMessage message={towerError} />
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Select Units <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="selectedUnits"
                      control={control}
                      render={({ field }) => (
                        <UnitSelector
                          value={field.value}
                          onChange={field.onChange}
                          selectedTowers={selectedTowers}
                        />
                      )}
                    />
                    {errors.selectedUnits && (
                      <ErrorMessage message={unitError} />
                    )}
                  </div>
                </div>

                {/* File Upload */}
                <div className="mb-4">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Attachments
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-md p-4">
                    <div className="flex items-center justify-center">
                      <Upload className="w-6 h-6 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-500">
                        Drag and drop files here or click to browse
                      </span>
                    </div>
                  </div>
                  {fileUploadError && (
                    <ErrorMessage message={fileUploadError} />
                  )}
                </div>
              </div>

              {/* Error Messages */}
              {formError && <ErrorMessage message={formError} />}
              {apiError && <ErrorMessage message={apiError} />}

              {/* Submit Button */}
              <div className="flex justify-center">
                <button
                  type="submit"
                  disabled={isSubmitting || !isFormValid()}
                  className={`w-full px-8 py-3 rounded-md transition duration-200 font-medium ${
                    isFormValid() && !isSubmitting
                      ? 'bg-[#3D9D9B] text-white hover:bg-[#34877A]'
                      : 'bg-white text-[#3D9D9B] border-2 border-[#3D9D9B] hover:bg-gray-50'
                  } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  {isSubmitting ? 'Creating...' : 'Send'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Success Message Box */}
      <MessageBox
        message={successMessage}
        clearMessage={clearMessage}
        onOk={handleSuccessOk}
      />
    </div>
  );
};

export default CreateDirectCommunication;
