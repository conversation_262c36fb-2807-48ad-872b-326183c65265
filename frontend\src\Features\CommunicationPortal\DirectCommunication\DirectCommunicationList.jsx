import { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { FaPlus, FaFlag, FaEye, FaImage, FaFilePdf, FaFileWord } from "react-icons/fa";
import { HiDotsHorizontal } from "react-icons/hi";
import { BiSearch, BiFilter } from "react-icons/bi";
import { MdKeyboardArrowDown } from "react-icons/md";
import { DirectCommunicationActionMenu, DirectCommunicationHistoryModal, usePinPost, PinIcon } from "./components";
import ConfirmationMessageBox from "../../../Components/MessageBox/ConfirmationMessageBox";
import ImageSlider from "../../../Components/Modal/ImageSlider";
import DocumentViewer from "../../../Components/FileViewer/DocumentViewer";
import { directCommunicationApi } from "../../../api/directCommunicationApi/directCommunicationBackendApi";

// Utility function to determine communication status based on dates
const getCommunicationStatus = (startDate, startTime, endDate, endTime, manuallyExpired = false) => {
  if (!startDate || !startTime || !endDate || !endTime) {
    return 'draft';
  }

  const now = new Date();
  const startDateTime = new Date(`${startDate} ${startTime}`);
  const endDateTime = new Date(`${endDate} ${endTime}`);

  // If manually expired and the actual end time hasn't passed yet, keep it expired
  if (manuallyExpired && now <= endDateTime) {
    return 'expired';
  }

  // Otherwise, use normal date-based logic
  if (now < startDateTime) {
    return 'upcoming';
  } else if (now >= startDateTime && now <= endDateTime) {
    return 'ongoing';
  } else {
    return 'expired';
  }
};

const DirectCommunicationList = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [communications, setCommunications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [showFilter, setShowFilter] = useState(false);
  const [selectedPriority, setSelectedPriority] = useState("");
  const [selectedLabel, setSelectedLabel] = useState("");
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [selectedCommunication, setSelectedCommunication] = useState(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [confirmationAction, setConfirmationAction] = useState(null);
  const [showImageSlider, setShowImageSlider] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [showDocumentViewer, setShowDocumentViewer] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const filterRef = useRef(null);

  // Get active tab from location state
  useEffect(() => {
    if (location.state?.activeTab) {
      setActiveTab(location.state.activeTab);
    }
  }, [location.state]);

  // Fetch communications
  useEffect(() => {
    const fetchCommunications = async () => {
      try {
        setLoading(true);
        const response = await directCommunicationApi.getAllDirectCommunications();
        setCommunications(response.data);
        setError(null);
      } catch (err) {
        setError("Failed to fetch communications");
        console.error("Error fetching communications:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchCommunications();
  }, []);

  // Handle tab change
  const handleTabChange = (tabIndex) => {
    setActiveTab(tabIndex);
  };

  // Handle search
  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  // Handle filter toggle
  const handleFilterToggle = () => {
    setShowFilter(!showFilter);
  };

  // Handle priority filter
  const handlePriorityFilter = (priority) => {
    setSelectedPriority(priority === selectedPriority ? "" : priority);
  };

  // Handle label filter
  const handleLabelFilter = (label) => {
    setSelectedLabel(label === selectedLabel ? "" : label);
  };

  // Handle create new communication
  const handleCreateNew = () => {
    navigate("/direct-communications/create", {
      state: { sourceTab: activeTab }
    });
  };

  // Handle view communication
  const handleView = (communication) => {
    navigate(`/direct-communications/${communication.id}`, {
      state: { sourceTab: activeTab }
    });
  };

  // Handle edit communication
  const handleEdit = (communication) => {
    navigate(`/direct-communications/${communication.id}/edit`, {
      state: { sourceTab: activeTab }
    });
  };

  // Handle delete communication
  const handleDelete = async (communication) => {
    try {
      await directCommunicationApi.deleteDirectCommunication(communication.id);
      setCommunications(communications.filter(c => c.id !== communication.id));
    } catch (err) {
      console.error("Error deleting communication:", err);
    }
  };

  // Handle pin/unpin communication
  const handlePinToggle = async (communication) => {
    try {
      await directCommunicationApi.togglePinDirectCommunication(
        communication.id,
        !communication.is_pinned
      );
      setCommunications(communications.map(c =>
        c.id === communication.id
          ? { ...c, is_pinned: !c.is_pinned }
          : c
      ));
    } catch (err) {
      console.error("Error toggling pin status:", err);
    }
  };

  // Handle view history
  const handleViewHistory = (communication) => {
    setSelectedCommunication(communication);
    setShowHistoryModal(true);
  };

  // Handle image click
  const handleImageClick = (communication, imageIndex) => {
    setSelectedCommunication(communication);
    setSelectedImageIndex(imageIndex);
    setShowImageSlider(true);
  };

  // Handle document click
  const handleDocumentClick = (communication, document) => {
    setSelectedCommunication(communication);
    setSelectedDocument(document);
    setShowDocumentViewer(true);
  };

  // Filter communications based on active tab, search, and filters
  const filteredCommunications = communications.filter(communication => {
    const status = getCommunicationStatus(
      communication.start_date,
      communication.start_time,
      communication.end_date,
      communication.end_time,
      communication.manually_expired
    );

    const matchesTab = (() => {
      switch (activeTab) {
        case 1: // Ongoing
          return status === 'ongoing';
        case 2: // Upcoming
          return status === 'upcoming';
        case 3: // Expired
          return status === 'expired';
        case 4: // Draft
          return status === 'draft';
        default:
          return true;
      }
    })();

    const matchesSearch = communication.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         communication.description.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesPriority = !selectedPriority || communication.priority === selectedPriority;
    const matchesLabel = !selectedLabel || communication.label === selectedLabel;

    return matchesTab && matchesSearch && matchesPriority && matchesLabel;
  });

  // Sort communications: pinned first, then by date
  const sortedCommunications = [...filteredCommunications].sort((a, b) => {
    if (a.is_pinned && !b.is_pinned) return -1;
    if (!a.is_pinned && b.is_pinned) return 1;
    return new Date(b.created_at) - new Date(a.created_at);
  });

  return (
    <div className="min-h-screen bg-[#F5F5F5]">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <h1 className="text-2xl font-semibold text-gray-900">Direct Communications</h1>
            <button
              onClick={handleCreateNew}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#3D9D9B] hover:bg-[#34877A] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#3D9D9B]"
            >
              <FaPlus className="mr-2" />
              Create New
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {['Ongoing', 'Upcoming', 'Expired', 'Draft'].map((tab, index) => (
              <button
                key={tab}
                onClick={() => handleTabChange(index + 1)}
                className={`${
                  activeTab === index + 1
                    ? 'border-[#3D9D9B] text-[#3D9D9B]'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                {tab}
              </button>
            ))}
          </nav>
        </div>

        {/* Search and Filter */}
        <div className="mt-4 flex items-center justify-between">
          <div className="flex-1 max-w-lg">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <BiSearch className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={searchQuery}
                onChange={handleSearch}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-[#3D9D9B] focus:border-[#3D9D9B] sm:text-sm"
                placeholder="Search communications..."
              />
            </div>
          </div>
          <div className="ml-4">
            <button
              onClick={handleFilterToggle}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#3D9D9B]"
            >
              <BiFilter className="mr-2" />
              Filter
              <MdKeyboardArrowDown className="ml-2" />
            </button>
          </div>
        </div>

        {/* Filter Panel */}
        {showFilter && (
          <div
            ref={filterRef}
            className="mt-4 bg-white rounded-lg shadow p-4"
          >
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Priority</h3>
                <div className="space-y-2">
                  {['urgent', 'high', 'normal', 'low'].map((priority) => (
                    <label key={priority} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedPriority === priority}
                        onChange={() => handlePriorityFilter(priority)}
                        className="h-4 w-4 text-[#3D9D9B] focus:ring-[#3D9D9B] border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700 capitalize">
                        {priority}
                      </span>
                    </label>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Label</h3>
                <div className="space-y-2">
                  {['Important', 'Update', 'Notice', 'Event'].map((label) => (
                    <label key={label} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedLabel === label}
                        onChange={() => handleLabelFilter(label)}
                        className="h-4 w-4 text-[#3D9D9B] focus:ring-[#3D9D9B] border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        {label}
                      </span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Communications List */}
        <div className="mt-6 space-y-4">
          {loading ? (
            <div className="text-center py-4">Loading...</div>
          ) : error ? (
            <div className="text-center py-4 text-red-600">{error}</div>
          ) : sortedCommunications.length === 0 ? (
            <div className="text-center py-4 text-gray-500">No communications found</div>
          ) : (
            sortedCommunications.map((communication) => (
              <div
                key={communication.id}
                className="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      {communication.is_pinned && (
                        <PinIcon className="text-[#3D9D9B]" />
                      )}
                      <h3 className="text-lg font-semibold text-gray-900">
                        {communication.title}
                      </h3>
                    </div>
                    <p className="mt-1 text-sm text-gray-600 line-clamp-2">
                      {communication.description}
                    </p>
                    <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                      <span>Posted by: {communication.creator_name}</span>
                      <span className="flex items-center">
                        <FaFlag className="mr-1" />
                        {communication.priority}
                      </span>
                      <span className="bg-gray-100 px-2 py-1 rounded-full">
                        {communication.label}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleView(communication)}
                      className="p-2 text-gray-400 hover:text-gray-500"
                    >
                      <FaEye className="h-5 w-5" />
                    </button>
                    <DirectCommunicationActionMenu
                      communication={communication}
                      onEdit={() => handleEdit(communication)}
                      onDelete={() => {
                        setSelectedCommunication(communication);
                        setConfirmationAction('delete');
                        setShowConfirmation(true);
                      }}
                      onPinToggle={() => handlePinToggle(communication)}
                      onViewHistory={() => handleViewHistory(communication)}
                    />
                  </div>
                </div>

                {/* Attachments */}
                {communication.attachments && communication.attachments.length > 0 && (
                  <div className="mt-4 flex items-center space-x-4">
                    {communication.attachments.map((attachment, index) => {
                      const isImage = attachment.file_type?.startsWith('image/');
                      const isPdf = attachment.file_type === 'application/pdf';
                      const isWord = attachment.file_type === 'application/msword' ||
                                   attachment.file_type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';

                      return (
                        <button
                          key={index}
                          onClick={() => {
                            if (isImage) {
                              handleImageClick(communication, index);
                            } else if (isPdf || isWord) {
                              handleDocumentClick(communication, attachment);
                            }
                          }}
                          className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-900"
                        >
                          {isImage && <FaImage className="text-blue-500" />}
                          {isPdf && <FaFilePdf className="text-red-500" />}
                          {isWord && <FaFileWord className="text-blue-600" />}
                          <span>{attachment.file_name}</span>
                        </button>
                      );
                    })}
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>

      {/* History Modal */}
      {showHistoryModal && selectedCommunication && (
        <DirectCommunicationHistoryModal
          communication={selectedCommunication}
          onClose={() => {
            setShowHistoryModal(false);
            setSelectedCommunication(null);
          }}
        />
      )}

      {/* Confirmation Dialog */}
      {showConfirmation && selectedCommunication && (
        <ConfirmationMessageBox
          message={`Are you sure you want to ${
            confirmationAction === 'delete' ? 'delete' : 'expire'
          } this communication?`}
          onConfirm={() => {
            if (confirmationAction === 'delete') {
              handleDelete(selectedCommunication);
            }
            setShowConfirmation(false);
            setSelectedCommunication(null);
            setConfirmationAction(null);
          }}
          onCancel={() => {
            setShowConfirmation(false);
            setSelectedCommunication(null);
            setConfirmationAction(null);
          }}
        />
      )}

      {/* Image Slider */}
      {showImageSlider && selectedCommunication && (
        <ImageSlider
          images={selectedCommunication.attachments
            .filter(att => att.file_type?.startsWith('image/'))
            .map(att => att.file_url)}
          initialIndex={selectedImageIndex}
          onClose={() => {
            setShowImageSlider(false);
            setSelectedCommunication(null);
            setSelectedImageIndex(0);
          }}
        />
      )}

      {/* Document Viewer */}
      {showDocumentViewer && selectedCommunication && selectedDocument && (
        <DocumentViewer
          document={selectedDocument}
          onClose={() => {
            setShowDocumentViewer(false);
            setSelectedCommunication(null);
            setSelectedDocument(null);
          }}
        />
      )}
    </div>
  );
};

export default DirectCommunicationList; 