import React, { useState, useRef, useEffect } from 'react';
import { HiDotsHorizontal } from 'react-icons/hi';
import { FaEdit, FaTrash, FaHistory, FaThumbtack } from 'react-icons/fa';

const DirectCommunicationActionMenu = ({
  communication,
  onEdit,
  onDelete,
  onPinToggle,
  onViewHistory
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleAction = (action) => {
    setIsOpen(false);
    action();
  };

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={handleToggle}
        className="p-2 text-gray-400 hover:text-gray-500 focus:outline-none"
      >
        <HiDotsHorizontal className="h-5 w-5" />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
          <div className="py-1" role="menu" aria-orientation="vertical">
            <button
              onClick={() => handleAction(onEdit)}
              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              role="menuitem"
            >
              <FaEdit className="mr-3 h-4 w-4" />
              Edit
            </button>

            <button
              onClick={() => handleAction(onPinToggle)}
              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              role="menuitem"
            >
              <FaThumbtack className="mr-3 h-4 w-4" />
              {communication.is_pinned ? 'Unpin' : 'Pin'}
            </button>

            <button
              onClick={() => handleAction(onViewHistory)}
              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              role="menuitem"
            >
              <FaHistory className="mr-3 h-4 w-4" />
              View History
            </button>

            <button
              onClick={() => handleAction(onDelete)}
              className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
              role="menuitem"
            >
              <FaTrash className="mr-3 h-4 w-4" />
              Delete
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DirectCommunicationActionMenu; 