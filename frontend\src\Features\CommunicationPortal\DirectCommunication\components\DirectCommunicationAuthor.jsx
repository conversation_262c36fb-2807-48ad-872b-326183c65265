import React from 'react';
import { Controller } from 'react-hook-form';
import PostAsSelector from '../../Announcements/components/PostAsSelector';
import MemberSelector from '../../Announcements/components/MemberSelector';
import GroupSelector from '../../Announcements/components/GroupSelector';
import ErrorMessage from '../../../../Components/MessageBox/ErrorMessage';

/**
 * DirectCommunicationAuthor Component
 * Handles the author selection for direct communications
 */
const DirectCommunicationAuthor = ({
  control,
  errors,
  currentUser,
  onMemberSelect,
  onGroupSelect,
  savePostAsPreference
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Direct Communication Author
      </h3>
      
      <div className="space-y-4">
        {/* Creator Name */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-2">
            Creator Name <span className="text-[#3D9D9B]">*</span>
          </label>
          <Controller
            name="creatorName"
            control={control}
            render={({ field }) => (
              <input
                {...field}
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3D9D9B] focus:border-transparent"
                placeholder="Enter creator name"
              />
            )}
          />
          {errors.creatorName && (
            <ErrorMessage message={errors.creatorName.message} />
          )}
        </div>

        {/* Post As Selector */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-2">
            Post as <span className="text-[#3D9D9B]">*</span>
          </label>
          <Controller
            name="postAs"
            control={control}
            render={({ field }) => (
              <PostAsSelector
                value={field.value}
                onChange={(value) => {
                  field.onChange(value);
                  savePostAsPreference(value);
                }}
                error={errors.postAs?.message}
              />
            )}
          />
        </div>

        {/* Member Selector - shown when postAs is 'Member' */}
        <Controller
          name="postAs"
          control={control}
          render={({ field: postAsField }) => (
            postAsField.value === 'Member' && (
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Select Member <span className="text-[#3D9D9B]">*</span>
                </label>
                <Controller
                  name="selectedMemberName"
                  control={control}
                  render={({ field: memberField }) => (
                    <MemberSelector
                      value={memberField.value}
                      onChange={onMemberSelect}
                      error={errors.selectedMemberName?.message}
                    />
                  )}
                />
              </div>
            )
          )}
        />

        {/* Group Selector - shown when postAs is 'Group' */}
        <Controller
          name="postAs"
          control={control}
          render={({ field: postAsField }) => (
            postAsField.value === 'Group' && (
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Select Group <span className="text-[#3D9D9B]">*</span>
                </label>
                <Controller
                  name="selectedGroupName"
                  control={control}
                  render={({ field: groupField }) => (
                    <GroupSelector
                      value={groupField.value}
                      onChange={onGroupSelect}
                      error={errors.selectedGroupName?.message}
                    />
                  )}
                />
              </div>
            )
          )}
        />
      </div>
    </div>
  );
};

export default DirectCommunicationAuthor;
