import React from 'react';
import { Controller } from 'react-hook-form';
import ErrorMessage from '../../../../Components/MessageBox/ErrorMessage';

/**
 * DirectCommunicationInformation Component
 * Handles the title and description for direct communications
 */
const DirectCommunicationInformation = ({
  control,
  errors,
  handleTitleChange
}) => {
  // Word count utility function
  const countWords = (text) => {
    if (!text || text.trim() === '') return 0;
    return text.trim().split(/\s+/).length;
  };

  // Handle description key down to limit words
  const handleDescriptionKeyDown = (e, currentValue) => {
    const currentWordCount = countWords(currentValue);

    // If we're under the limit, allow all typing
    if (currentWordCount < 100) {
      return; // Allow the keystroke
    }

    // If we're at or over 100 words, block everything except backspace and delete
    if (currentWordCount >= 100) {
      // Only allow backspace and delete to remove content
      if (e.key === 'Backspace' || e.key === 'Delete') {
        return; // Allow deletion
      }

      // Block everything else
      e.preventDefault();
      return;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Direct Communication Information
      </h3>
      
      <div className="space-y-4">
        {/* Title */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-2">
            Title <span className="text-[#3D9D9B]">*</span>
          </label>
          <Controller
            name="title"
            control={control}
            render={({ field }) => (
              <div>
                <input
                  type="text"
                  value={field.value}
                  onChange={(e) => handleTitleChange(e.target.value, field.onChange)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3D9D9B] focus:border-[#3D9D9B]"
                  placeholder="Enter direct communication title..."
                  maxLength={200}
                />
                <div className="flex justify-between items-center mt-1">
                  <div>
                    {errors.title && (
                      <ErrorMessage message={errors.title.message} />
                    )}
                  </div>
                  <div className="text-xs text-gray-500">
                    {countWords(field.value)}/10 words
                  </div>
                </div>
              </div>
            )}
          />
        </div>

        {/* Reminder Text */}
        <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
          <p className="text-sm text-blue-700">
            <strong>Reminder:</strong> Direct Communications can be about new policies.
          </p>
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-2">
            Description <span className="text-[#3D9D9B]">*</span>
          </label>
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <div>
                <textarea
                  value={field.value}
                  onChange={field.onChange}
                  onKeyDown={(e) => handleDescriptionKeyDown(e, field.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3D9D9B] focus:border-[#3D9D9B] resize-none"
                  placeholder="Enter direct communication description..."
                  rows={6}
                />
                <div className="flex justify-between items-center mt-1">
                  <div>
                    {errors.description && (
                      <ErrorMessage message={errors.description.message} />
                    )}
                  </div>
                  <div className="text-xs text-gray-500">
                    {countWords(field.value)}/100 words
                  </div>
                </div>
              </div>
            )}
          />
        </div>
      </div>
    </div>
  );
};

export default DirectCommunicationInformation;
