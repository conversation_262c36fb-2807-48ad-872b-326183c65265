import React from 'react';
import { format } from 'date-fns';
import { FaFlag, FaImage, FaFilePdf, FaFileWord } from 'react-icons/fa';

const DirectCommunicationPreview = ({ data, currentUser }) => {
  const {
    title,
    description,
    postAs,
    creatorName,
    selectedMemberName,
    selectedGroupName,
    priority,
    label,
    startDate,
    startTime,
    endDate,
    endTime,
    attachments = []
  } = data;

  // Format dates
  const formatDateTime = (date, time) => {
    if (!date || !time) return '';
    const dateTime = new Date(`${date} ${time}`);
    return format(dateTime, 'MMM dd, yyyy hh:mm a');
  };

  // Get priority color
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600';
      case 'high':
        return 'text-orange-600';
      case 'normal':
        return 'text-blue-600';
      case 'low':
        return 'text-green-600';
      default:
        return 'text-gray-600';
    }
  };

  // Get author name based on post type
  const getAuthorName = () => {
    switch (postAs) {
      case 'Member':
        return selectedMemberName;
      case 'Group':
        return selectedGroupName;
      default:
        return creatorName;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b pb-4">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">{title || 'Communication Title'}</h2>
        <div className="flex items-center space-x-4 text-sm text-gray-600">
          <span>Posted by: {getAuthorName() || 'Author Name'}</span>
          {priority && (
            <span className={`flex items-center ${getPriorityColor(priority)}`}>
              <FaFlag className="mr-1" />
              {priority.charAt(0).toUpperCase() + priority.slice(1)}
            </span>
          )}
          {label && (
            <span className="bg-gray-100 px-2 py-1 rounded-full text-xs">
              {label}
            </span>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="prose max-w-none">
        <p className="text-gray-700 whitespace-pre-wrap">
          {description || 'Communication description will appear here...'}
        </p>
      </div>

      {/* Date and Time */}
      {(startDate || endDate) && (
        <div className="border-t pt-4">
          <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
            <div>
              <p className="font-semibold">Start</p>
              <p>{formatDateTime(startDate, startTime) || 'Not set'}</p>
            </div>
            <div>
              <p className="font-semibold">End</p>
              <p>{formatDateTime(endDate, endTime) || 'Not set'}</p>
            </div>
          </div>
        </div>
      )}

      {/* Attachments */}
      {attachments && attachments.length > 0 && (
        <div className="border-t pt-4">
          <h3 className="text-sm font-semibold text-gray-700 mb-2">Attachments</h3>
          <div className="space-y-2">
            {attachments.map((attachment, index) => {
              const isImage = attachment.type?.startsWith('image/');
              const isPdf = attachment.type === 'application/pdf';
              const isWord = attachment.type === 'application/msword' || 
                           attachment.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';

              return (
                <div key={index} className="flex items-center space-x-2 text-sm text-gray-600">
                  {isImage && <FaImage className="text-blue-500" />}
                  {isPdf && <FaFilePdf className="text-red-500" />}
                  {isWord && <FaFileWord className="text-blue-600" />}
                  <span>{attachment.name}</span>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default DirectCommunicationPreview; 