import React from 'react';
import { Controller } from 'react-hook-form';
import TowerSelector from '../../Announcements/components/TowerSelector';
import UnitSelector from '../../Announcements/components/UnitSelector';
import ErrorMessage from '../../../../Components/MessageBox/ErrorMessage';

/**
 * DirectCommunicationVisibility Component
 * Handles tower and unit selection for direct communications
 */
const DirectCommunicationVisibility = ({
  control,
  errors
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Direct Communication Visibility
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Tower Selection */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-2">
            Tower <span className="text-[#3D9D9B]">*</span>
          </label>
          <Controller
            name="selectedTowers"
            control={control}
            render={({ field }) => (
              <TowerSelector
                value={field.value}
                onChange={field.onChange}
                error={errors.selectedTowers?.message}
              />
            )}
          />
          {errors.selectedTowers && (
            <ErrorMessage message={errors.selectedTowers.message} />
          )}
        </div>

        {/* Unit Selection */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-2">
            Unit <span className="text-[#3D9D9B]">*</span>
          </label>
          <Controller
            name="selectedUnits"
            control={control}
            render={({ field }) => (
              <Controller
                name="selectedTowers"
                control={control}
                render={({ field: towerField }) => (
                  <UnitSelector
                    value={field.value}
                    onChange={field.onChange}
                    selectedTowers={towerField.value}
                    error={errors.selectedUnits?.message}
                  />
                )}
              />
            )}
          />
          {errors.selectedUnits && (
            <ErrorMessage message={errors.selectedUnits.message} />
          )}
        </div>
      </div>

      {/* Selection Summary */}
      <Controller
        name="selectedTowers"
        control={control}
        render={({ field: towerField }) => (
          <Controller
            name="selectedUnits"
            control={control}
            render={({ field: unitField }) => (
              (towerField.value?.length > 0 || unitField.value?.length > 0) && (
                <div className="mt-4 p-3 bg-gray-50 rounded-md">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Selection Summary:</h4>
                  <div className="text-sm text-gray-600">
                    {towerField.value?.length > 0 && (
                      <p>Towers: {towerField.value.length} selected</p>
                    )}
                    {unitField.value?.length > 0 && (
                      <p>Units: {unitField.value.length} selected</p>
                    )}
                  </div>
                </div>
              )
            )}
          />
        )}
      />
    </div>
  );
};

export default DirectCommunicationVisibility;
