import React from 'react';
import { Controller } from 'react-hook-form';
import { Mail, Phone, MessageCircle } from 'lucide-react';
import ErrorMessage from '../../../../Components/MessageBox/ErrorMessage';

/**
 * SendDirectCommunication Component
 * Handles communication method selection and form submission
 */
const SendDirectCommunication = ({
  control,
  errors,
  isSubmitting,
  onSubmit
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Send Direct Communication
      </h3>
      
      <div className="space-y-4">
        {/* Communication Methods */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-3">
            Select Communication Methods <span className="text-[#3D9D9B]">*</span>
          </label>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Email Option */}
            <Controller
              name="communicationMethods.email"
              control={control}
              render={({ field }) => (
                <div className="flex items-center space-x-3 p-3 border border-gray-300 rounded-md hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    id="email"
                    checked={field.value}
                    onChange={field.onChange}
                    className="w-4 h-4 text-[#3D9D9B] border-gray-300 rounded focus:ring-[#3D9D9B]"
                  />
                  <label htmlFor="email" className="flex items-center space-x-2 cursor-pointer">
                    <Mail className="w-5 h-5 text-blue-600" />
                    <span className="text-sm font-medium text-gray-700">Email (30)</span>
                  </label>
                </div>
              )}
            />

            {/* Phone Option */}
            <Controller
              name="communicationMethods.phone"
              control={control}
              render={({ field }) => (
                <div className="flex items-center space-x-3 p-3 border border-gray-300 rounded-md hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    id="phone"
                    checked={field.value}
                    onChange={field.onChange}
                    className="w-4 h-4 text-[#3D9D9B] border-gray-300 rounded focus:ring-[#3D9D9B]"
                  />
                  <label htmlFor="phone" className="flex items-center space-x-2 cursor-pointer">
                    <Phone className="w-5 h-5 text-green-600" />
                    <span className="text-sm font-medium text-gray-700">Phone Number (30)</span>
                  </label>
                </div>
              )}
            />

            {/* WhatsApp Option */}
            <Controller
              name="communicationMethods.whatsapp"
              control={control}
              render={({ field }) => (
                <div className="flex items-center space-x-3 p-3 border border-gray-300 rounded-md hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    id="whatsapp"
                    checked={field.value}
                    onChange={field.onChange}
                    className="w-4 h-4 text-[#3D9D9B] border-gray-300 rounded focus:ring-[#3D9D9B]"
                  />
                  <label htmlFor="whatsapp" className="flex items-center space-x-2 cursor-pointer">
                    <MessageCircle className="w-5 h-5 text-green-500" />
                    <span className="text-sm font-medium text-gray-700">WhatsApp (30)</span>
                  </label>
                </div>
              )}
            />
          </div>

          {/* Communication Methods Error */}
          {errors.communicationMethods && (
            <ErrorMessage message={errors.communicationMethods.message} />
          )}
        </div>

        {/* Send Button */}
        <div className="flex justify-end pt-4">
          <button
            type="submit"
            disabled={isSubmitting}
            onClick={onSubmit}
            className={`px-8 py-3 rounded-md font-medium text-white transition-colors ${
              isSubmitting
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-[#3D9D9B] hover:bg-[#2d7a78] focus:outline-none focus:ring-2 focus:ring-[#3D9D9B] focus:ring-offset-2'
            }`}
          >
            {isSubmitting ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Sending...</span>
              </div>
            ) : (
              'Send'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SendDirectCommunication;
