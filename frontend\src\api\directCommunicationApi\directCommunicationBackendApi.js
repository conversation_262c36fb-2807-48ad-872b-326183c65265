import axios from 'axios';
import { API_BASE_URL } from '../config';

// API endpoints
const ENDPOINTS = {
  DIRECT_COMMUNICATIONS: `${API_BASE_URL}/direct-communications`,
  DIRECT_COMMUNICATION: (id) => `${API_BASE_URL}/direct-communications/${id}`,
};

// Format data for API submission
export const formatDirectCommunicationForApi = (data, attachments) => {
  const {
    title,
    description,
    postAs,
    creatorName,
    selectedMemberId,
    selectedMemberName,
    selectedGroupId,
    selectedGroupName,
    priority,
    label,
    startDate,
    startTime,
    endDate,
    endTime,
    selectedTowers,
    selectedUnits,
  } = data;

  return {
    title,
    description,
    post_as: postAs.toLowerCase(),
    creator_name: creatorName,
    posted_member: postAs === 'Member' ? selectedMemberId : null,
    member_name: postAs === 'Member' ? selectedMemberName : null,
    posted_group: postAs === 'Group' ? selectedGroupId : null,
    group_name: postAs === 'Group' ? selectedGroupName : null,
    priority,
    label,
    start_date: startDate,
    start_time: startTime,
    end_date: endDate,
    end_time: endTime,
    target_towers: selectedTowers,
    target_units: selectedUnits,
    attachments: attachments.map(attachment => ({
      file_name: attachment.name,
      file_type: attachment.type,
      file_url: attachment.url,
    })),
  };
};

// API methods
export const directCommunicationApi = {
  // Get all direct communications
  getAllDirectCommunications: async (params = {}) => {
    try {
      const response = await axios.get(ENDPOINTS.DIRECT_COMMUNICATIONS, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching direct communications:', error);
      throw error;
    }
  },

  // Get a single direct communication
  getDirectCommunication: async (id) => {
    try {
      const response = await axios.get(ENDPOINTS.DIRECT_COMMUNICATION(id));
      return response.data;
    } catch (error) {
      console.error(`Error fetching direct communication ${id}:`, error);
      throw error;
    }
  },

  // Create a new direct communication
  createDirectCommunication: async (data) => {
    try {
      const response = await axios.post(ENDPOINTS.DIRECT_COMMUNICATIONS, data);
      return response.data;
    } catch (error) {
      console.error('Error creating direct communication:', error);
      throw error;
    }
  },

  // Update an existing direct communication
  updateDirectCommunication: async (id, data) => {
    try {
      const response = await axios.put(ENDPOINTS.DIRECT_COMMUNICATION(id), data);
      return response.data;
    } catch (error) {
      console.error(`Error updating direct communication ${id}:`, error);
      throw error;
    }
  },

  // Delete a direct communication
  deleteDirectCommunication: async (id) => {
    try {
      const response = await axios.delete(ENDPOINTS.DIRECT_COMMUNICATION(id));
      return response.data;
    } catch (error) {
      console.error(`Error deleting direct communication ${id}:`, error);
      throw error;
    }
  },

  // Pin/Unpin a direct communication
  togglePinDirectCommunication: async (id, isPinned) => {
    try {
      const response = await axios.patch(ENDPOINTS.DIRECT_COMMUNICATION(id), {
        is_pinned: isPinned,
      });
      return response.data;
    } catch (error) {
      console.error(`Error toggling pin status for direct communication ${id}:`, error);
      throw error;
    }
  },

  // Mark a direct communication as read
  markAsRead: async (id) => {
    try {
      const response = await axios.patch(`${ENDPOINTS.DIRECT_COMMUNICATION(id)}/read`);
      return response.data;
    } catch (error) {
      console.error(`Error marking direct communication ${id} as read:`, error);
      throw error;
    }
  },

  // Get direct communication statistics
  getDirectCommunicationStats: async () => {
    try {
      const response = await axios.get(`${ENDPOINTS.DIRECT_COMMUNICATIONS}/stats`);
      return response.data;
    } catch (error) {
      console.error('Error fetching direct communication statistics:', error);
      throw error;
    }
  },
}; 