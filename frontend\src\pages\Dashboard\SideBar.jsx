import { NavLink, useNavigate } from "react-router-dom";
import { FaFileAlt } from "react-icons/fa";
import { BsMegaphone } from "react-icons/bs";
import { FaAngleDown, FaAngleRight } from "react-icons/fa6";

import { useState } from "react";
import { TbBuildingEstate } from "react-icons/tb";
import { HiMiniUserGroup } from "react-icons/hi2";
import { PiBuildingApartment } from "react-icons/pi";
import { IoWalletOutline } from "react-icons/io5";
import { CiLogout } from "react-icons/ci";
import { RiDashboardHorizontalLine } from "react-icons/ri";
import { useDispatch } from "react-redux";
import { logoutUser } from "../../api/authApi/authApi";

//  Created By Firoj Hasan
const Sidebar = ({ isOpen, toggleSidebar }) => {
  const [activeMenu, setActiveMenu] = useState(null);

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const handleLogout = (e) => {
    e.preventDefault();
    const result = dispatch(logoutUser(navigate));
    if (result && result.error) {
      setErrorMessage(parseError(result.error));
    }
  };

  const toggleMenu = (menuName) => {
    setActiveMenu((prev) => (prev === menuName ? null : menuName));
  };

  return (
    <div className="bg-white h-[100%] shadow-sm mt-3">
      <ul className="space-y-4 text-center py-4">
        <li>
          <NavLink
            to="/"
            className={({ isActive }) =>
              `flex items-center pl-6 py-3 text-sm sm:text-base md:text-[12px] lg:text-[12px] xl:text-[18px] ${
                isActive
                  ? "bg-primary text-white"
                  : "bg-white hover:bg-primary hover:text-white text-primary"
              }`
            }
          >
            <RiDashboardHorizontalLine className="mr-[8px] w-5 h-5 sm:w-6 sm:h-6 md:w-6 md:h-6 lg:w-6 lg:h-6 xl:w-7 xl:h-7" />
            <span>Dashboard</span>
          </NavLink>
        </li>

        <ul className="mt-0">
          <li className="pl-6  bg-white hover:bg-primary hover:text-white text-primary text-sm sm:text-base md:text-[12px] lg:text-[12px] xl:text-[18px]">
            <button
              onClick={() => toggleMenu("memberManagement")}
              className="flex items-center py-3"
            >
              <HiMiniUserGroup className="mr-[8px] w-5 h-5 sm:w-6 sm:h-6 md:w-6 md:h-6 lg:w-6 lg:h-6 xl:w-7 xl:h-7" />
              <span>Member Management</span>
              {activeMenu === "memberManagement" ? (
                <FaAngleDown className="pl-[8px] w-5 h-5" />
              ) : (
                <FaAngleRight className="pl-[8px] w-4 h-4" />
              )}
            </button>
          </li>

          {activeMenu === "memberManagement" && (
            <ul className="submenu pl-6">
              <li>
                <NavLink
                  to="/member-list"
                  className={({ isActive }) =>
                    `flex items-center pl-6 py-3 text-sm sm:text-base md:text-[12px] lg:text-[12px] xl:text-[18px] ${
                      isActive
                        ? "bg-primary text-white"
                        : "bg-white hover:bg-primary hover:text-white text-primary"
                    }`
                  }
                >
                  <span>Organization Members</span>
                </NavLink>
              </li>
              <li>
                <NavLink
                  to="/roleList"
                  className={({ isActive }) =>
                    `flex items-center pl-6 py-3 text-sm sm:text-base md:text-[12px] lg:text-[12px] xl:text-[18px] ${
                      isActive
                        ? "bg-primary text-white"
                        : "bg-white hover:bg-primary hover:text-white text-primary"
                    }`
                  }
                >
                  <span>Role Management</span>
                </NavLink>
              </li>
              <li>
                <NavLink
                  to="/groups"
                  className={({ isActive }) =>
                    `flex items-center pl-6 py-3 text-sm sm:text-base md:text-[12px] lg:text-[12px] xl:text-[18px] ${
                      isActive
                        ? "bg-primary text-white"
                        : "bg-white hover:bg-primary hover:text-white text-primary"
                    }`
                  }
                >
                  <span>Groups</span>
                </NavLink>
              </li>
            </ul>
          )}

          <li className="pl-6  bg-white hover:bg-primary hover:text-white text-primary text-sm sm:text-base md:text-[12px] lg:text-[12px] xl:text-[18px]">
            <button
              onClick={() => toggleMenu("towerManagement")}
              className="flex items-center py-3"
            >
              <TbBuildingEstate className="mr-[2px] w-5 h-5 sm:w-6 sm:h-6 md:w-6 md:h-6 lg:w-6 lg:h-6 xl:w-7 xl:h-7" />
              <span>Tower & Unit Management</span>
              {activeMenu === "towerManagement" ? (
                <FaAngleDown className="pl-[8px] w-5 h-5" />
              ) : (
                <FaAngleRight className="pl-[8px] w-4 h-4" />
              )}
            </button>
          </li>

          {activeMenu === "towerManagement" && (
            <ul className="submenu pl-6">
              <li>
                <NavLink
                  to="/ViewTowers"
                  className={({ isActive }) =>
                    `flex items-center pl-6 py-3 text-sm sm:text-base md:text-[12px] lg:text-[12px] xl:text-[18px] ${
                      isActive
                        ? "bg-primary text-white"
                        : "bg-white hover:bg-primary hover:text-white text-primary"
                    }`
                  }
                >
                  <span>Tower & Unit Management</span>
                </NavLink>
              </li>
              <li className="bg-white hover:bg-primary hover:text-white text-primary text-sm sm:text-base md:text-[12px] lg:text-[12px] xl:text-[18px]">
                <NavLink
                  to="/community-memberList"
                  className={({ isActive }) =>
                    `flex items-center pl-6 py-3 ${
                      isActive
                        ? "bg-primary text-white"
                        : "bg-white hover:bg-primary hover:text-white text-primary"
                    }`
                  }
                >
                  <span>Community Member Management</span>
                </NavLink>

              </li>
            </ul>
          )}
        </ul>

        <li className="pl-6 bg-white hover:bg-primary hover:text-white text-primary text-sm sm:text-base md:text-[12px] lg:text-[12px] xl:text-[18px]" style={{ marginTop: "0px" }}>
          <button
            onClick={() => toggleMenu("communication")}
            className="flex items-center py-3"
          >
            <BsMegaphone className="mr-[8px] w-5 h-5 sm:w-6 sm:h-6 md:w-6 md:h-6 lg:w-6 lg:h-6 xl:w-7 xl:h-7" />
            <span>Communication Portal</span>
            {activeMenu === "communication" ? (
              <FaAngleDown className="pl-[8px] w-5 h-5" />
            ) : (
              <FaAngleRight className="pl-[8px] w-4 h-4" />
            )}
          </button>
        </li>

        {activeMenu === "communication" && (
          <ul className="submenu pl-6">
            <li>
              <NavLink
                to="/announcements"
                className={({ isActive }) =>
                  `flex items-center pl-8 py-1 text-sm sm:text-base md:text-[12px] lg:text-[12px] xl:text-[18px] ${
                    isActive
                      ? "bg-primary text-white"
                      : "bg-white hover:bg-primary hover:text-white text-primary"
                  }`
                }
              >
                <span>Announcements</span>
              </NavLink>
            </li>
            <li>
              <NavLink
                to="/create-direct-communication"
                className={({ isActive }) =>
                  `flex items-center pl-8 py-1 text-sm sm:text-base md:text-[12px] lg:text-[12px] xl:text-[18px] ${
                    isActive
                      ? "bg-primary text-white"
                      : "bg-white hover:bg-primary hover:text-white text-primary"
                  }`
                }
              >
                <span>Direct Communication</span>
              </NavLink>
            </li>
          </ul>
        )}

        <li style={{ marginTop: "0px" }}>
          <NavLink
            to="/amenity"
            className={({ isActive }) =>
              `flex items-center pl-6 py-3 text-sm sm:text-base md:text-[12px] lg:text-[12px] xl:text-[18px] ${
                isActive
                  ? "bg-primary text-white"
                  : "bg-white hover:bg-primary hover:text-white text-primary"
              }`
            }
          >
            <PiBuildingApartment className="mr-[8px] w-5 h-5 sm:w-6 sm:h-6 md:w-6 md:h-6 lg:w-6 lg:h-6 xl:w-7 xl:h-7" />
            <span>Amenity Management</span>
          </NavLink>
        </li>

        <li style={{ marginTop: "0px" }}>
          <NavLink
            to="/service-fee"
            className={({ isActive }) =>
              `flex items-center pl-6 py-3 text-sm sm:text-base md:text-[12px] lg:text-[12px] xl:text-[18px] ${
                isActive
                  ? "bg-primary text-white"
                  : "bg-white hover:bg-primary hover:text-white text-primary"
              }`
            }
          >
            <IoWalletOutline className="mr-[8px] w-5 h-5 sm:w-6 sm:h-6 md:w-6 md:h-6 lg:w-6 lg:h-6 xl:w-7 xl:h-7" />
            <span>Service Fee Management</span>
          </NavLink>
        </li>

        <li style={{ marginTop: "0px" }}>
          <NavLink
            to="/report"
            className={({ isActive }) =>
              `flex items-center pl-6 py-3 text-sm sm:text-base md:text-[12px] lg:text-[12px] xl:text-[18px] ${
                isActive
                  ? "bg-primary text-white"
                  : "bg-white hover:bg-primary hover:text-white text-primary"
              }`
            }
          >
            <FaFileAlt className="mr-[8px] w-5 h-5 sm:w-6 sm:h-6 md:w-6 md:h-6 lg:w-6 lg:h-6 xl:w-7 xl:h-7" />
            <span>Report</span>
          </NavLink>
        </li>

        <li style={{ marginTop: "0px" }}>
          <NavLink
            to="/logout"
            onClick={handleLogout}
            className={({ isActive }) =>
              `flex items-center pl-6 py-3 text-sm sm:text-base md:text-[12px] lg:text-[12px] xl:text-[18px] ${
                isActive
                  ? "bg-primary text-white"
                  : "bg-white hover:bg-primary hover:text-white text-primary"
              }`
            }
          >
            <CiLogout className="mr-[8px] w-5 h-5 sm:w-6 sm:h-6 md:w-6 md:h-6 lg:w-6 lg:h-6 xl:w-7 xl:h-7" />
            <span>Logout</span>
          </NavLink>
        </li>
      </ul>
    </div>
  );
};

export default Sidebar;
